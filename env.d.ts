/// <reference types="@react-router/dev" />

// Enhance TypeScript's built-in typings.
import '@total-typescript/ts-reset';

import type { Storefront, HydrogenCart, CustomerAccount, HydrogenSessionData } from '@shopify/hydrogen';
import type { SessionClient } from '@/business/clients/session-client';
import type <PERSON><PERSON> from 'stripe';
import type { ADCClient } from '@/business/clients/adc-client';
import type { CustomerAccessToken } from '@shopify/hydrogen/storefront-api-types';
import type { ShopifySessionService } from './app/business/clients/shopify-client';
import type { AdminClient } from '@/business/clients/admin-client';
import type { DependencyContainer } from 'tsyringe';
import { MultipassCustomer } from '@/business/clients/multipass-client';

type ShopifyApp = Awaited<ReturnType<(typeof ShopifySessionService)['initializeShopify']>>;

declare global {
  /**
   * A global `process` object is only available during build to access NODE_ENV.
   */
  const process: { env: { NODE_ENV: 'production' | 'development' } };

  /**
   * Declare expected Env parameter in fetch handler.
   */
  interface Env {
    SHOP_ID: string;
    SESSION_SECRET: string;
    PUBLIC_STOREFRONT_API_TOKEN: string;
    PRIVATE_STOREFRONT_API_TOKEN: string;
    PUBLIC_STORE_DOMAIN: string;
    PUBLIC_STOREFRONT_ID: string;
    PUBLIC_CUSTOMER_ACCOUNT_API_CLIENT_ID: string;
    PUBLIC_CUSTOMER_ACCOUNT_API_URL: string;
    PUBLIC_CHECKOUT_DOMAIN: string;
    PUBLIC_BUILDER_KEY: string;
    OIDC_CLIENT_ID: string;
    OIDC_CLIENT_SECRET: string;
    OIDC_PRIVATE_JWK_B64: string;
    OIDC_PUBLIC_JWKS_B64: string;
    ADC_SSO_USERNAME: string;
    ADC_SSO_PASSWORD: string;
    SENDGRID_TRANSACTIONAL_API_KEY: string;
    SENDGRID_MARKETING_API_KEY: string;
    SENDGRID_OTP_TEMPLATE_ID: string;
    SENDGRID_SSO_TEMPLATE_ID: string;
    TWILIO_ACCOUNT_SID: string;
    TWILIO_AUTH_TOKEN: string;
    TWILIO_SENDER: string;
    MULTIPASS_SECRET: string;
    STRIPE_SECRET_KEY: string;
    STRIPE_PUBLISHABLE_KEY: string;
    ADC_AUTH_USERNAME: string;
    ADC_AUTH_PASSWORD: string;
    ADC_AUTH_CLIENT_ID: string;
    ADC_AUTH_2FA_ID: string;
    ADC_DEALER_ID: string;
    ADC_WEBHOOK_SECRET: string;
    GEMINI_API_KEY: string;
    PLATE_TO_VIN_API_KEY: string;
    SHOPIFY_API_KEY: string;
    SHOPIFY_API_SECRET: string;
    SHOPIFY_OFFLINE_ADMIN_ACCESS_TOKEN: string;
    GTM_CONTAINER_ID: string;
    HOST: string;
    SCOPES: string;
  }

  type WaitUntil = (promise: Promise<any>) => void;

  type SystemType = 'personal' | 'business';

  type OIDCState = {
    email?: string;
    signature?: string;
    expiresAt?: number;
    multipassToken?: string;
  };
}

declare module 'react-router' {
  // TODO: remove this once we've migrated to `Route.LoaderArgs` for our loaders
  interface LoaderFunctionArgs {
    context: AppLoadContext;
  }

  // TODO: remove this once we've migrated to `Route.ActionArgs` for our actions
  interface ActionFunctionArgs {
    context: AppLoadContext;
  }

  /**
   * Declare local additions to the React Router loader context.
   */
  export interface AppLoadContext extends DependencyContainer {}

  /**
   * Declare local additions to the Remix session data.
   */
  interface SessionData extends HydrogenSessionData {
    browsingSystemType: SystemType;
    accountSession: {
      ownerId: string | undefined;
      systemKey: string | undefined;
    };
    OIDC: OIDCState;
    stripeCheckoutId: string | undefined;
  }
}
