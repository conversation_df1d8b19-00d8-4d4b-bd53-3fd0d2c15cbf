# Node.js/Express Server Setup

Your Hydrogen app has been successfully migrated from Shopify's Oxygen runtime to a standard Node.js/Express server.

## Changes Made

### 1. Dependencies
- **Added**: `express`, `@types/express`, `compression`, `@types/compression`
- **Removed**: `@shopify/remix-oxygen`, `@shopify/mini-oxygen`, `@shopify/oxygen-workers-types`

### 2. Server Configuration
- **New server file**: `server.js` - Express server that replaces the Oxygen worker
- **Updated context**: `app/lib/context.ts` - Modified to work with Node.js instead of Cloudflare Workers
- **Updated entry server**: `app/entry.server.tsx` - Removed Oxygen-specific imports

### 3. Build Configuration
- **Updated scripts**: Modified `package.json` scripts for React Router v7
- **Updated Vite config**: Removed Oxygen plugin
- **Updated TypeScript**: Removed Oxygen type references

### 4. Import Updates
- All `@shopify/remix-oxygen` imports replaced with `react-router`
- Updated type definitions for Node.js environment

## Running the Application

### Development
```bash
npm run dev:h2
```

### Production Build
```bash
npm run build
```

### Start Production Server
```bash
npm run start
```

### Preview (Build + Start)
```bash
npm run preview
```

## Environment Variables

Copy `.env.example` to `.env` and configure your environment variables:

```bash
cp .env.example .env
```

Required variables include:
- `SESSION_SECRET`
- `SHOP_ID`
- `PUBLIC_STOREFRONT_API_TOKEN`
- `PRIVATE_STOREFRONT_API_TOKEN`
- `PUBLIC_STORE_DOMAIN`
- And many others (see `.env.example`)

## Server Configuration

The Express server (`server.js`) includes:
- Static file serving from `dist/client`
- Compression middleware
- Request handling with React Router
- Session management
- Hydrogen context creation
- Error handling

## Deployment

For deployment, you can now use any Node.js hosting platform:
- **Heroku**: Use the provided `server.js` as your entry point
- **Railway**: Configure with `npm run build && npm run start`
- **DigitalOcean App Platform**: Use Node.js buildpack
- **AWS EC2/ECS**: Deploy as a standard Node.js application
- **Docker**: Create a Dockerfile that runs `npm run build && npm run start`

### Docker Example
```dockerfile
FROM node:20-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "run", "start"]
```

## Key Differences from Oxygen

1. **Runtime**: Now runs on Node.js instead of Cloudflare Workers
2. **Caching**: Uses in-memory cache instead of Cloudflare's cache API
3. **Request handling**: Express middleware instead of fetch handlers
4. **Environment**: Standard Node.js environment variables
5. **Deployment**: Can deploy to any Node.js hosting platform

## Troubleshooting

### Build Issues
- Ensure Node.js version >= 20 (React Router v7 requirement)
- Check that all environment variables are set
- Verify all dependencies are installed

### Runtime Issues
- Check server logs for detailed error messages
- Ensure all required environment variables are configured
- Verify database/API connections if applicable

### Performance
- The in-memory cache is suitable for development but consider Redis for production
- Monitor memory usage as the cache grows
- Consider implementing cache TTL for production use

## Next Steps

1. Test the application thoroughly in your development environment
2. Update your deployment pipeline to use the new build process
3. Configure production environment variables
4. Set up monitoring and logging for the Node.js server
5. Consider implementing Redis for production caching if needed
