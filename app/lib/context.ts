import { CART_QUERY_FRAGMENT } from '@/business/handlers/constants/fragments';
import { createHydrogenContext, createWithCache } from '@shopify/hydrogen';
import { container } from 'tsyringe';
import { SessionClient } from '@/business/clients/session-client';
import { StorefrontClient } from '@/business/clients/storefront-client';
import { CartClient } from '@/business/clients/cart-client';
import { CustomerAccountClient } from '@/business/clients/accounts-client';

// Node.js cache implementation
class NodeCache {
  private cache = new Map();

  async match(request) {
    const key = typeof request === 'string' ? request : request.url;
    return this.cache.get(key);
  }

  async put(request, response) {
    const key = typeof request === 'string' ? request : request.url;
    this.cache.set(key, response);
  }

  async delete(request) {
    const key = typeof request === 'string' ? request : request.url;
    return this.cache.delete(key);
  }
}

const nodeCache = new NodeCache();

export async function createAppLoadContext(
  request: any, // Express request or standard Request
  env: any,
  executionContext: any,
) {
  /**
   * Create session and cache instances for Node.js environment
   */
  if (!env?.SESSION_SECRET) {
    throw new Error('SESSION_SECRET environment variable is not set');
  }

  const waitUntil = executionContext.waitUntil;
  const cache = nodeCache;

  // Convert Express request to standard Request if needed
  const standardRequest = request.url
    ? new Request(`${request.protocol}://${request.get('host')}${request.originalUrl}`, {
        method: request.method,
        headers: request.headers,
        body: request.method !== 'GET' && request.method !== 'HEAD' ? JSON.stringify(request.body) : undefined,
      })
    : request;

  const session = await SessionClient.init(standardRequest, [env.SESSION_SECRET]);

  const withCache = createWithCache({ cache, waitUntil, request: standardRequest });
  const context = createHydrogenContext({
    env,
    request: standardRequest,
    cache,
    waitUntil,
    session,
    i18n: { language: 'EN', country: 'US' },
    cart: {
      queryFragment: CART_QUERY_FRAGMENT,
    },
  });

  const app = container.createChildContainer();
  // app.register('version', { useValue: Math.floor(Math.random() * 100000) });
  app.register('request', { useValue: standardRequest });
  app.register('env', { useValue: context.env });
  app.register('waitUntil', { useValue: context.waitUntil });
  app.register('withCache', { useValue: withCache });
  app.register(SessionClient, { useValue: context.session });
  app.register(StorefrontClient, { useValue: new StorefrontClient(context.storefront) });
  app.register(CartClient, { useValue: new CartClient(context.cart) });
  app.register(CustomerAccountClient, { useValue: new CustomerAccountClient(context.customerAccount) });

  return app;
}
