// Enable tSyringe
import '@abraham/reflection';
import express from 'express';
import compression from 'compression';
import pkg from '@react-router/node';
const { createRequestHandler } = pkg;
import { storefrontRedirect } from '@shopify/hydrogen';

const app = express();

// Enable compression
app.use(compression());

// Serve static files from the build directory
app.use(
  express.static('dist/client', {
    maxAge: '1y',
    setHeaders: (res, path) => {
      // Don't cache HTML files
      if (path.endsWith('.html')) {
        res.setHeader('Cache-Control', 'no-cache');
      }
    },
  }),
);

// Handle all other requests with React Router
app.all('*', async (req, res, next) => {
  try {
    const now = Date.now();

    // Create a mock environment object for Node.js
    const env = {
      SESSION_SECRET: process.env.SESSION_SECRET,
      SHOP_ID: process.env.SHOP_ID,
      PUBLIC_STOREFRONT_API_TOKEN: process.env.PUBLIC_STOREFRONT_API_TOKEN,
      PRIVATE_STOREFRONT_API_TOKEN: process.env.PRIVATE_STOREFRONT_API_TOKEN,
      PUBLIC_STORE_DOMAIN: process.env.PUBLIC_STORE_DOMAIN,
      PUBLIC_STOREFRONT_ID: process.env.PUBLIC_STOREFRONT_ID,
      PUBLIC_CUSTOMER_ACCOUNT_API_CLIENT_ID: process.env.PUBLIC_CUSTOMER_ACCOUNT_API_CLIENT_ID,
      PUBLIC_CUSTOMER_ACCOUNT_API_URL: process.env.PUBLIC_CUSTOMER_ACCOUNT_API_URL,
      PUBLIC_CHECKOUT_DOMAIN: process.env.PUBLIC_CHECKOUT_DOMAIN,
      PUBLIC_BUILDER_KEY: process.env.PUBLIC_BUILDER_KEY,
      OIDC_CLIENT_ID: process.env.OIDC_CLIENT_ID,
      OIDC_CLIENT_SECRET: process.env.OIDC_CLIENT_SECRET,
      OIDC_PRIVATE_JWK_B64: process.env.OIDC_PRIVATE_JWK_B64,
      OIDC_PUBLIC_JWKS_B64: process.env.OIDC_PUBLIC_JWKS_B64,
      ADC_SSO_USERNAME: process.env.ADC_SSO_USERNAME,
      ADC_SSO_PASSWORD: process.env.ADC_SSO_PASSWORD,
      SENDGRID_TRANSACTIONAL_API_KEY: process.env.SENDGRID_TRANSACTIONAL_API_KEY,
      SENDGRID_MARKETING_API_KEY: process.env.SENDGRID_MARKETING_API_KEY,
      SENDGRID_OTP_TEMPLATE_ID: process.env.SENDGRID_OTP_TEMPLATE_ID,
      SENDGRID_SSO_TEMPLATE_ID: process.env.SENDGRID_SSO_TEMPLATE_ID,
      TWILIO_ACCOUNT_SID: process.env.TWILIO_ACCOUNT_SID,
      TWILIO_AUTH_TOKEN: process.env.TWILIO_AUTH_TOKEN,
      TWILIO_SENDER: process.env.TWILIO_SENDER,
      MULTIPASS_SECRET: process.env.MULTIPASS_SECRET,
      STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY,
      STRIPE_PUBLISHABLE_KEY: process.env.STRIPE_PUBLISHABLE_KEY,
      ADC_AUTH_USERNAME: process.env.ADC_AUTH_USERNAME,
      ADC_AUTH_PASSWORD: process.env.ADC_AUTH_PASSWORD,
      ADC_AUTH_CLIENT_ID: process.env.ADC_AUTH_CLIENT_ID,
      ADC_AUTH_2FA_ID: process.env.ADC_AUTH_2FA_ID,
      ADC_DEALER_ID: process.env.ADC_DEALER_ID,
      ADC_WEBHOOK_SECRET: process.env.ADC_WEBHOOK_SECRET,
      GEMINI_API_KEY: process.env.GEMINI_API_KEY,
      PLATE_TO_VIN_API_KEY: process.env.PLATE_TO_VIN_API_KEY,
      SHOPIFY_API_KEY: process.env.SHOPIFY_API_KEY,
      SHOPIFY_API_SECRET: process.env.SHOPIFY_API_SECRET,
      SHOPIFY_OFFLINE_ADMIN_ACCESS_TOKEN: process.env.SHOPIFY_OFFLINE_ADMIN_ACCESS_TOKEN,
      GTM_CONTAINER_ID: process.env.GTM_CONTAINER_ID,
      HOST: process.env.HOST,
      SCOPES: process.env.SCOPES,
    };

    // Create a mock execution context for Node.js
    const executionContext = {
      waitUntil: promise => {
        // In Node.js, we can just let promises resolve naturally
        promise.catch(console.error);
      },
    };

    // Import the build and get the context creation function
    const build = await import('./dist/server/index.js');
    const { createAppLoadContext, SessionClient, StorefrontClient, CustomerAccountClient } = build;

    const context = await createAppLoadContext(req, env, executionContext);
    const session = context.resolve(SessionClient);
    const storefront = context.resolve(StorefrontClient);
    const customerAccount = context.resolve(CustomerAccountClient);

    // Add legacy properties for compatibility
    context.session = session;
    context.storefront = storefront;
    context.customerAccount = customerAccount;

    // Create React Router request handler
    const handleRequest = createRequestHandler({
      build: build.default || build,
      mode: process.env.NODE_ENV || 'production',
      getLoadContext: () => context,
    });

    // Convert Express request to Web API Request
    const webRequest = new Request(`${req.protocol}://${req.get('host')}${req.originalUrl}`, {
      method: req.method,
      headers: req.headers,
      body: req.method !== 'GET' && req.method !== 'HEAD' ? JSON.stringify(req.body) : undefined,
    });

    const response = await handleRequest(webRequest);

    if (session.isPending) {
      res.setHeader('Set-Cookie', await session.commit());
    }

    res.setHeader(
      'Server-Timing',
      `hydrogen;desc="Full hydrogen process from beginning to end";dur=${Date.now() - now}`,
    );

    // Handle 404s with storefront redirect
    if (response && response.status === 404) {
      const redirectResponse = await storefrontRedirect({
        request: webRequest,
        response,
        storefront,
      });

      if (redirectResponse.status !== 404) {
        return res.redirect(redirectResponse.status, redirectResponse.headers.get('Location'));
      }
    }

    // Convert Web API Response back to Express response
    if (response) {
      res.status(response.status);

      // Copy headers
      for (const [key, value] of response.headers.entries()) {
        res.setHeader(key, value);
      }

      // Send body
      if (response.body) {
        const reader = response.body.getReader();
        const pump = () => {
          return reader.read().then(({ done, value }) => {
            if (done) {
              res.end();
              return;
            }
            res.write(value);
            return pump();
          });
        };
        return pump();
      } else {
        res.end();
      }
    }
  } catch (error) {
    console.error(error);
    res.status(500).send('An unexpected error occurred');
  }
});

const port = process.env.PORT || 3000;
app.listen(port, () => {
  console.log(`Server running on port ${port}`);
});
