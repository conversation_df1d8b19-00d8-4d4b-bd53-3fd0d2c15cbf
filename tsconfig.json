{
  "include": ["./**/*.d.ts", "./**/*.ts", "./**/*.tsx", ".react-router/types/**/*", ".graphqlrc.ts"],

  "exclude": ["node_modules", "dist"],
  "compilerOptions": {
    "rootDirs": [".", "./.react-router/types"],
    "lib": ["DOM", "DOM.Iterable", "ES2022"],
    "isolatedModules": true,
    "esModuleInterop": true,
    "jsx": "react-jsx",
    "moduleResolution": "Bundler",
    "resolveJsonModule": true,
    "module": "ES2022",
    "target": "ES2022",
    "strict": true,
    "allowJs": true,
    "forceConsistentCasingInFileNames": true,
    "noImplicitAny": true,
    "skipLibCheck": true,
    "baseUrl": ".",
    "types": ["jest", "node"],
    "paths": {
      "@/*": ["./app/*"]
    },
    "noEmit": true,
    //Enables tSyringe support
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true
  }
}
